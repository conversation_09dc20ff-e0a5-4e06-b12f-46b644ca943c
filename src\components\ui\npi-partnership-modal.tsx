'use client'

import React, { useState } from 'react'
import { NPIModal } from '@/components/ui/npi-modal'
import { NPIButton } from '@/components/ui/npi-button'
import {
  User,
  Mail,
  Phone,
  Building,
  Globe,
  FileText,
  DollarSign,
  Users,
  Target,
  CheckCircle,
  X,
} from 'lucide-react'

interface PartnershipApplicationModalProps {
  isOpen: boolean
  onClose: () => void
  selectedModel?: string
}

interface FormData {
  // Organization Details
  organizationName: string
  organizationType: string
  website: string
  establishedYear: string

  // Contact Information
  contactName: string
  contactTitle: string
  email: string
  phone: string

  // Partnership Details
  partnershipModel: string
  investmentCapacity: string
  projectInterest: string
  timeline: string

  // Additional Information
  experience: string
  objectives: string
  additionalInfo: string
}

const initialFormData: FormData = {
  organizationName: '',
  organizationType: '',
  website: '',
  establishedYear: '',
  contactName: '',
  contactTitle: '',
  email: '',
  phone: '',
  partnershipModel: '',
  investmentCapacity: '',
  projectInterest: '',
  timeline: '',
  experience: '',
  objectives: '',
  additionalInfo: '',
}

const organizationTypes = [
  'Private Company',
  'NGO/Non-Profit',
  'Government Agency',
  'Academic Institution',
  'International Organization',
  'Foundation',
  'Cooperative',
  'Other',
]

const partnershipModels = [
  'Strategic Partnership',
  'Investment Partnership',
  'Technical Partnership',
  'Community Partnership',
  'Research Partnership',
]

const investmentRanges = [
  'Under KES 1M',
  'KES 1M - 5M',
  'KES 5M - 20M',
  'KES 20M - 50M',
  'KES 50M - 100M',
  'Over KES 100M',
]

const timelines = [
  'Immediate (0-3 months)',
  'Short-term (3-6 months)',
  'Medium-term (6-12 months)',
  'Long-term (1-2 years)',
  'Strategic (2+ years)',
]

export const PartnershipApplicationModal: React.FC<PartnershipApplicationModalProps> = ({
  isOpen,
  onClose,
  selectedModel = '',
}) => {
  const [formData, setFormData] = useState<FormData>({
    ...initialFormData,
    partnershipModel: selectedModel,
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
  ) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000))
      setSubmitStatus('success')

      // Reset form after successful submission
      setTimeout(() => {
        setFormData(initialFormData)
        setSubmitStatus('idle')
        onClose()
      }, 2000)
    } catch (error) {
      setSubmitStatus('error')
    } finally {
      setIsSubmitting(false)
    }
  }

  const resetForm = () => {
    setFormData(initialFormData)
    setSubmitStatus('idle')
  }

  const handleClose = () => {
    resetForm()
    onClose()
  }

  if (submitStatus === 'success') {
    return (
      <NPIModal isOpen={isOpen} onClose={handleClose} size="md">
        <div className="p-8 text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h2 className="text-2xl font-bold text-[#34170D] mb-4 font-npi">
            Application Submitted Successfully!
          </h2>
          <p className="text-[#46372A] mb-6 font-npi">
            Thank you for your interest in partnering with NPI. Our team will review your
            application and contact you within 3-5 business days.
          </p>
          <NPIButton onClick={handleClose} variant="primary" size="lg">
            Close
          </NPIButton>
        </div>
      </NPIModal>
    )
  }

  return (
    <NPIModal isOpen={isOpen} onClose={handleClose} size="xl">
      <div className="p-6 max-h-[80vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6 pb-4 border-b border-[#8D8F78]/30">
          <div>
            <h2 className="text-2xl font-bold text-[#34170D] font-npi">Partnership Application</h2>
            <p className="text-[#46372A] font-npi">
              Join us in transforming Kenya&apos;s natural products sector
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Organization Details */}
          <div className="bg-[#E5E1DC]/50 p-4 border border-[#8D8F78]/30">
            <h3 className="text-lg font-semibold text-[#34170D] mb-4 font-npi flex items-center gap-2">
              <Building className="w-5 h-5" />
              Organization Details
            </h3>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[#46372A] mb-2 font-npi">
                  Organization Name *
                </label>
                <input
                  type="text"
                  name="organizationName"
                  value={formData.organizationName}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-[#8D8F78]/50 bg-white text-[#2F2C29] font-npi text-sm focus:outline-none focus:ring-2 focus:ring-[#A7795E] focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[#46372A] mb-2 font-npi">
                  Organization Type *
                </label>
                <select
                  name="organizationType"
                  value={formData.organizationType}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-[#8D8F78]/50 bg-white text-[#2F2C29] font-npi text-sm focus:outline-none focus:ring-2 focus:ring-[#A7795E] focus:border-transparent"
                >
                  <option value="">Select type...</option>
                  {organizationTypes.map((type) => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-[#46372A] mb-2 font-npi">
                  Website
                </label>
                <input
                  type="url"
                  name="website"
                  value={formData.website}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-[#8D8F78]/50 bg-white text-[#2F2C29] font-npi text-sm focus:outline-none focus:ring-2 focus:ring-[#A7795E] focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[#46372A] mb-2 font-npi">
                  Year Established
                </label>
                <input
                  type="number"
                  name="establishedYear"
                  value={formData.establishedYear}
                  onChange={handleInputChange}
                  min="1900"
                  max={new Date().getFullYear()}
                  className="w-full px-3 py-2 border border-[#8D8F78]/50 bg-white text-[#2F2C29] font-npi text-sm focus:outline-none focus:ring-2 focus:ring-[#A7795E] focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-[#CEC9BC]/30 p-4 border border-[#8D8F78]/30">
            <h3 className="text-lg font-semibold text-[#34170D] mb-4 font-npi flex items-center gap-2">
              <User className="w-5 h-5" />
              Contact Information
            </h3>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[#46372A] mb-2 font-npi">
                  Contact Name *
                </label>
                <input
                  type="text"
                  name="contactName"
                  value={formData.contactName}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-[#8D8F78]/50 bg-white text-[#2F2C29] font-npi text-sm focus:outline-none focus:ring-2 focus:ring-[#A7795E] focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[#46372A] mb-2 font-npi">
                  Title/Position *
                </label>
                <input
                  type="text"
                  name="contactTitle"
                  value={formData.contactTitle}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-[#8D8F78]/50 bg-white text-[#2F2C29] font-npi text-sm focus:outline-none focus:ring-2 focus:ring-[#A7795E] focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[#46372A] mb-2 font-npi">
                  Email Address *
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-[#8D8F78]/50 bg-white text-[#2F2C29] font-npi text-sm focus:outline-none focus:ring-2 focus:ring-[#A7795E] focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[#46372A] mb-2 font-npi">
                  Phone Number
                </label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-[#8D8F78]/50 bg-white text-[#2F2C29] font-npi text-sm focus:outline-none focus:ring-2 focus:ring-[#A7795E] focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Partnership Details */}
          <div className="bg-[#CABA9C]/20 p-4 border border-[#8D8F78]/30">
            <h3 className="text-lg font-semibold text-[#34170D] mb-4 font-npi flex items-center gap-2">
              <Target className="w-5 h-5" />
              Partnership Details
            </h3>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[#46372A] mb-2 font-npi">
                  Preferred Partnership Model *
                </label>
                <select
                  name="partnershipModel"
                  value={formData.partnershipModel}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-[#8D8F78]/50 bg-white text-[#2F2C29] font-npi text-sm focus:outline-none focus:ring-2 focus:ring-[#A7795E] focus:border-transparent"
                >
                  <option value="">Select model...</option>
                  {partnershipModels.map((model) => (
                    <option key={model} value={model}>
                      {model}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-[#46372A] mb-2 font-npi">
                  Investment Capacity
                </label>
                <select
                  name="investmentCapacity"
                  value={formData.investmentCapacity}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-[#8D8F78]/50 bg-white text-[#2F2C29] font-npi text-sm focus:outline-none focus:ring-2 focus:ring-[#A7795E] focus:border-transparent"
                >
                  <option value="">Select range...</option>
                  {investmentRanges.map((range) => (
                    <option key={range} value={range}>
                      {range}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-[#46372A] mb-2 font-npi">
                  Project Interest Areas
                </label>
                <input
                  type="text"
                  name="projectInterest"
                  value={formData.projectInterest}
                  onChange={handleInputChange}
                  placeholder="e.g., Aloe processing, Essential oils, Community development"
                  className="w-full px-3 py-2 border border-[#8D8F78]/50 bg-white text-[#2F2C29] font-npi text-sm focus:outline-none focus:ring-2 focus:ring-[#A7795E] focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[#46372A] mb-2 font-npi">
                  Timeline
                </label>
                <select
                  name="timeline"
                  value={formData.timeline}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-[#8D8F78]/50 bg-white text-[#2F2C29] font-npi text-sm focus:outline-none focus:ring-2 focus:ring-[#A7795E] focus:border-transparent"
                >
                  <option value="">Select timeline...</option>
                  {timelines.map((timeline) => (
                    <option key={timeline} value={timeline}>
                      {timeline}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="bg-[#4C6444]/10 p-4 border border-[#8D8F78]/30">
            <h3 className="text-lg font-semibold text-[#34170D] mb-4 font-npi flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Additional Information
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-[#46372A] mb-2 font-npi">
                  Relevant Experience
                </label>
                <textarea
                  name="experience"
                  value={formData.experience}
                  onChange={handleInputChange}
                  rows={3}
                  placeholder="Describe your organization's relevant experience in natural products, community development, or related sectors..."
                  className="w-full px-3 py-2 border border-[#8D8F78]/50 bg-white text-[#2F2C29] font-npi text-sm focus:outline-none focus:ring-2 focus:ring-[#A7795E] focus:border-transparent resize-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[#46372A] mb-2 font-npi">
                  Partnership Objectives
                </label>
                <textarea
                  name="objectives"
                  value={formData.objectives}
                  onChange={handleInputChange}
                  rows={3}
                  placeholder="What do you hope to achieve through this partnership? How does it align with your organization's mission?"
                  className="w-full px-3 py-2 border border-[#8D8F78]/50 bg-white text-[#2F2C29] font-npi text-sm focus:outline-none focus:ring-2 focus:ring-[#A7795E] focus:border-transparent resize-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[#46372A] mb-2 font-npi">
                  Additional Comments
                </label>
                <textarea
                  name="additionalInfo"
                  value={formData.additionalInfo}
                  onChange={handleInputChange}
                  rows={3}
                  placeholder="Any additional information you'd like to share..."
                  className="w-full px-3 py-2 border border-[#8D8F78]/50 bg-white text-[#2F2C29] font-npi text-sm focus:outline-none focus:ring-2 focus:ring-[#A7795E] focus:border-transparent resize-none"
                />
              </div>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-[#8D8F78]/30">
            <NPIButton
              type="submit"
              variant="primary"
              size="lg"
              disabled={isSubmitting}
              className="flex-1"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Application'}
            </NPIButton>
            <NPIButton
              type="button"
              variant="outline"
              size="lg"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </NPIButton>
          </div>

          {submitStatus === 'error' && (
            <div className="bg-red-50 border border-red-200 p-4 text-center">
              <p className="text-red-600 font-npi">
                There was an error submitting your application. Please try again.
              </p>
            </div>
          )}
        </form>
      </div>
    </NPIModal>
  )
}
