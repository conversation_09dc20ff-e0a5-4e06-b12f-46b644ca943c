'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { NPIParallaxHero } from '@/components/ui/npi-parallax-hero'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import { TrendingUp, DollarSign, Target, ArrowDown } from 'lucide-react'

interface NPIInvestmentOpportunitiesHeroProps {
  title?: string
  subtitle?: string
  totalOpportunities?: number
  averageROI?: string
  fundingAvailable?: string
}

export const NPIInvestmentOpportunitiesHeroBlock: React.FC<NPIInvestmentOpportunitiesHeroProps> = ({
  title = 'Investment Opportunities',
  subtitle = "Discover high-impact investment opportunities in Kenya's natural products sector. Join us in creating sustainable economic growth while preserving traditional knowledge and empowering communities.",
  totalOpportunities = 150,
  averageROI = '15-25%',
  fundingAvailable = 'KES 5B+',
}) => {
  return (
    <NPIParallaxHero
      backgroundVideo="/assets/AZhkaMy0gnRKNo3HTx72YA-AZhkaMy0-rC2BAvdMT8JPw.mp4"
      height="large"
      parallaxSpeed={0.3}
      overlayOpacity={0.7}
      className="relative min-h-[70vh] max-h-[85vh] -mt-16 pt-16"
    >
      {/* Custom Layout Container - Absolute positioning for extreme corners */}
      <div className="absolute inset-0 text-white z-20">
        {/* Top Left Section - Aligned with navbar container */}
        <motion.div
          className="absolute top-0 left-0 pt-26 px-4 sm:px-6 lg:px-25 max-w-7xl"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className="max-w-lg space-y-6">
            {/* Organization info */}
            <motion.p
              className="text-sm md:text-base text-white/90 font-light"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              Natural Products Industry Initiative
            </motion.p>

            {/* Main title */}
            <motion.h1
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight font-npi"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              {title}
            </motion.h1>

            {/* Subtitle */}
            <motion.p
              className="text-lg md:text-xl text-white/80 font-light leading-relaxed max-w-md"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              {subtitle}
            </motion.p>
          </div>
        </motion.div>

        {/* Bottom Right Section - Statistics and Actions */}
        <motion.div
          className="absolute bottom-0 right-0 pb-5 px-4 sm:px-6 lg:px-25 max-w-7xl"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 1.0 }}
        >
          <div className="max-w-lg space-y-6">
            {/* Key Statistics */}
            <motion.div
              className="grid grid-cols-3 gap-4 mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.2 }}
            >
              <div className="bg-[#8A3E25]/30 backdrop-blur-md border border-[#725242]/40 p-4 text-center">
                <div className="text-2xl font-bold text-white mb-1 font-npi">
                  {totalOpportunities}
                </div>
                <div className="text-white/80 text-sm font-npi">Opportunities</div>
              </div>
              <div className="bg-[#8A3E25]/30 backdrop-blur-md border border-[#725242]/40 p-4 text-center">
                <div className="text-2xl font-bold text-white mb-1 font-npi">{averageROI}</div>
                <div className="text-white/80 text-sm font-npi">Avg ROI</div>
              </div>
              <div className="bg-[#8A3E25]/30 backdrop-blur-md border border-[#725242]/40 p-4 text-center">
                <div className="text-2xl font-bold text-white mb-1 font-npi">
                  {fundingAvailable}
                </div>
                <div className="text-white/80 text-sm font-npi">Available</div>
              </div>
            </motion.div>

            {/* Action Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.4 }}
            >
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <NPIButton
                  asChild
                  size="lg"
                  className="border-2 border-[#8A3E25] bg-[#8A3E25] text-white hover:bg-[#8A3E25] hover:border-[#8A3E25] backdrop-blur-md px-8 py-4 transition-all duration-300 shadow-lg hover:shadow-xl font-bold"
                >
                  <Link href="#opportunities" className="flex items-center gap-2">
                    <ArrowDown className="w-5 h-5" />
                    View Opportunities
                  </Link>
                </NPIButton>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <NPIButton
                  asChild
                  size="lg"
                  className="border-2 border-[#25718A] bg-[#25718A] text-white hover:bg-[#25718A] hover:border-[#25718A] backdrop-blur-md px-8 py-4 transition-all duration-300 shadow-lg hover:shadow-xl font-bold"
                >
                  <Link href="/contact" className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Schedule Consultation
                  </Link>
                </NPIButton>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </NPIParallaxHero>
  )
}
