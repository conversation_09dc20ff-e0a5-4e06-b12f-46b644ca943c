import type { Metadata } from 'next'
import { NPIHeroComponent } from '@/heros/NPIHero'
import {
  NPIFeaturedProjectsBlock,
  NPISuccessStoriesBlock,
  NPILatestUpdatesBlock,
} from '@/blocks/pages/home'
import PageClient from './[slug]/page.client'

export default function HomePage() {
  return (
    <article className="min-h-screen">
      <PageClient />

      {/* Hero Section */}
      <NPIHeroComponent />

      {/* Main Content Sections - Compact Layout with Alternating Backgrounds */}
      <main className="relative">
        {/* Featured Projects Section - White Background */}
        <section className="py-4 lg:py-6 relative bg-white">
          <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
            <NPIFeaturedProjectsBlock />
          </div>
        </section>

        {/* Success Stories Section - Cream Background */}
        <section className="py-4 lg:py-6 relative bg-[#EFE3BA]">
          <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
            <NPISuccessStoriesBlock />
          </div>
        </section>

        {/* Latest Updates Section - White Background */}
        <section className="py-4 lg:py-6 relative bg-white">
          <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
            <NPILatestUpdatesBlock />
          </div>
        </section>
      </main>
    </article>
  )
}

export async function generateMetadata(): Promise<Metadata> {
  return {
    title:
      'Natural Products Industry Initiative - Kenya | Harnessing Indigenous Wealth for Sustainable Growth',
    description:
      "Transforming Kenya's Vision 2030 into reality through sustainable natural products development. Harnessing indigenous knowledge and natural resources for economic development.",
    openGraph: {
      title: 'Natural Products Industry Initiative - Kenya',
      description:
        'Harnessing Indigenous Wealth for Sustainable Growth - Natural Products Industry Initiative Kenya',
      type: 'website',
      url: '/',
    },
  }
}
