'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPI<PERSON>ard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Image from 'next/image'
import { X, ExternalLink, MapPin, Calendar, Users } from 'lucide-react'

interface Partner {
  id: string
  name: string
  logo: string
  category: string
  location: string
  established: string
  description: string
  fullBio: string
  website?: string
  keyAchievements: string[]
  collaborationAreas: string[]
  partnershipSince: string
}

interface NPIPartnersShowcaseProps {
  title?: string
  description?: string
  partners?: Partner[]
}

export const NPIPartnersShowcaseBlock: React.FC<NPIPartnersShowcaseProps> = ({
  title = 'Partner Network',
  description = "Discover the diverse organizations and institutions that collaborate with us to drive sustainable development in Kenya's natural products sector.",
  partners = [
    {
      id: 'partner-1',
      name: 'Partner 1',
      logo: 'https://picsum.photos/200/200?random=1',
      category: 'Government Initiative',
      location: 'Nairobi, Kenya',
      established: '2008',
      description:
        'Strategic government partnership focused on policy development and economic alignment.',
      fullBio:
        'Partner 1 represents a key government initiative that aligns with our natural products development goals. This partnership focuses on policy framework development and strategic economic planning to support sustainable growth in the natural products sector.',
      website: 'https://example.com',
      keyAchievements: [
        'Policy framework development for natural products',
        'Integration of traditional knowledge systems',
        'Economic pillar alignment with NPI objectives',
      ],
      collaborationAreas: ['Policy Development', 'Economic Planning', 'Strategic Alignment'],
      partnershipSince: '2019',
    },
    {
      id: 'partner-2',
      name: 'Partner 2',
      logo: 'https://picsum.photos/200/200?random=2',
      category: 'Cultural Institution',
      location: 'Mombasa, Kenya',
      established: '1910',
      description:
        'Cultural institution dedicated to preserving traditional knowledge and heritage.',
      fullBio:
        'Partner 2 is a leading cultural institution that collaborates with us on documenting traditional knowledge systems, preserving cultural practices related to natural products, and ensuring that indigenous wisdom is respected and protected.',
      website: 'https://example.com',
      keyAchievements: [
        'Traditional knowledge documentation',
        'Cultural heritage preservation',
        'Community engagement programs',
      ],
      collaborationAreas: [
        'Cultural Preservation',
        'Traditional Knowledge',
        'Community Engagement',
      ],
      partnershipSince: '2020',
    },
    {
      id: 'partner-3',
      name: 'Partner 3',
      logo: 'https://picsum.photos/200/200?random=3',
      category: 'Research Institution',
      location: 'Kisumu, Kenya',
      established: '1995',
      description:
        'Leading research institution advancing natural products science and innovation.',
      fullBio:
        'Partner 3 is a premier research institution that conducts cutting-edge research in natural products development, sustainable extraction methods, and community-based innovation programs.',
      website: 'https://example.com',
      keyAchievements: [
        'Breakthrough research in natural compounds',
        'Sustainable extraction technology development',
        'Community-based research programs',
      ],
      collaborationAreas: ['Research & Development', 'Innovation', 'Technology Transfer'],
      partnershipSince: '2018',
    },
    {
      id: 'partner-4',
      name: 'Partner 4',
      logo: 'https://picsum.photos/200/200?random=4',
      category: 'International NGO',
      location: 'Eldoret, Kenya',
      established: '2005',
      description:
        'International organization supporting sustainable development and community empowerment.',
      fullBio:
        'Partner 4 is an international NGO that works closely with local communities to promote sustainable development practices and empower communities through natural products initiatives.',
      website: 'https://example.com',
      keyAchievements: [
        'Community empowerment programs',
        'Sustainable development initiatives',
        'International funding facilitation',
      ],
      collaborationAreas: ['Community Development', 'Sustainability', 'International Relations'],
      partnershipSince: '2021',
    },
    {
      id: 'partner-5',
      name: 'Partner 5',
      logo: 'https://picsum.photos/200/200?random=5',
      category: 'Private Sector',
      location: 'Nakuru, Kenya',
      established: '2010',
      description: 'Private sector leader in natural products manufacturing and distribution.',
      fullBio:
        'Partner 5 is a leading private sector company that specializes in natural products manufacturing, quality control, and market distribution, providing valuable industry expertise and market access.',
      website: 'https://example.com',
      keyAchievements: [
        'Market expansion initiatives',
        'Quality assurance systems',
        'Supply chain optimization',
      ],
      collaborationAreas: ['Manufacturing', 'Quality Control', 'Market Access'],
      partnershipSince: '2022',
    },
    {
      id: 'partner-6',
      name: 'Partner 6',
      logo: 'https://picsum.photos/200/200?random=6',
      category: 'Academic Institution',
      location: 'Thika, Kenya',
      established: '1970',
      description: 'Academic institution providing education and research in natural sciences.',
      fullBio:
        'Partner 6 is a renowned academic institution that offers specialized programs in natural sciences and conducts collaborative research projects that advance our understanding of natural products.',
      website: 'https://example.com',
      keyAchievements: [
        'Educational program development',
        'Student research initiatives',
        'Academic publication contributions',
      ],
      collaborationAreas: ['Education', 'Research', 'Capacity Building'],
      partnershipSince: '2017',
    },
    {
      id: 'partner-7',
      name: 'Partner 7',
      logo: 'https://picsum.photos/200/200?random=7',
      category: 'Community Organization',
      location: 'Machakos, Kenya',
      established: '2012',
      description:
        'Community-based organization promoting local development and traditional practices.',
      fullBio:
        'Partner 7 is a grassroots community organization that works directly with local communities to preserve traditional practices and promote sustainable development through natural products initiatives.',
      website: 'https://example.com',
      keyAchievements: [
        'Community mobilization programs',
        'Traditional practice preservation',
        'Local capacity building',
      ],
      collaborationAreas: ['Community Engagement', 'Traditional Practices', 'Local Development'],
      partnershipSince: '2023',
    },
    {
      id: 'partner-8',
      name: 'Partner 8',
      logo: 'https://picsum.photos/200/200?random=8',
      category: 'Technology Partner',
      location: 'Nyeri, Kenya',
      established: '2015',
      description: 'Technology company providing digital solutions for natural products sector.',
      fullBio:
        'Partner 8 is an innovative technology company that develops digital solutions, data management systems, and technological tools that enhance efficiency and transparency in the natural products sector.',
      website: 'https://example.com',
      keyAchievements: [
        'Digital platform development',
        'Data management solutions',
        'Technology integration programs',
      ],
      collaborationAreas: ['Technology Development', 'Digital Solutions', 'Data Management'],
      partnershipSince: '2021',
    },
  ],
}) => {
  const [selectedPartner, setSelectedPartner] = useState<Partner | null>(null)

  const getCardDesign = (index: number) => {
    const designs = [
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#725242]',
        text: 'text-black',
        logoContainer: 'bg-[#725242]/15 border-[#725242]/40',
        categoryBg: 'bg-[#725242] text-white',
        buttonHover: 'hover:bg-[#725242]/15',
        iconColor: 'text-[#725242]',
        divider: 'border-[#725242]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#8A3E25]',
        text: 'text-black',
        logoContainer: 'bg-[#8A3E25]/15 border-[#8A3E25]/40',
        categoryBg: 'bg-[#8A3E25] text-white',
        buttonHover: 'hover:bg-[#8A3E25]/15',
        iconColor: 'text-[#8A3E25]',
        divider: 'border-[#8A3E25]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#25718A]',
        text: 'text-black',
        logoContainer: 'bg-[#25718A]/15 border-[#25718A]/40',
        categoryBg: 'bg-[#25718A] text-white',
        buttonHover: 'hover:bg-[#25718A]/15',
        iconColor: 'text-[#25718A]',
        divider: 'border-[#25718A]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#725242]',
        text: 'text-black',
        logoContainer: 'bg-white border-[#725242]/40',
        categoryBg: 'bg-[#725242] text-white',
        buttonHover: 'hover:bg-[#725242]/15',
        iconColor: 'text-[#725242]',
        divider: 'border-[#725242]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#8A3E25]',
        text: 'text-black',
        logoContainer: 'bg-white border-[#8A3E25]/40',
        categoryBg: 'bg-[#8A3E25] text-white',
        buttonHover: 'hover:bg-[#8A3E25]/15',
        iconColor: 'text-[#8A3E25]',
        divider: 'border-[#8A3E25]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#25718A]',
        text: 'text-black',
        logoContainer: 'bg-white border-[#25718A]/40',
        categoryBg: 'bg-[#25718A] text-white',
        buttonHover: 'hover:bg-[#25718A]/15',
        iconColor: 'text-[#25718A]',
        divider: 'border-[#25718A]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#725242]',
        text: 'text-black',
        logoContainer: 'bg-[#725242]/15 border-[#725242]/40',
        categoryBg: 'bg-[#725242] text-white',
        buttonHover: 'hover:bg-[#725242]/15',
        iconColor: 'text-[#725242]',
        divider: 'border-[#725242]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#8A3E25]',
        text: 'text-black',
        logoContainer: 'bg-[#8A3E25]/15 border-[#8A3E25]/40',
        categoryBg: 'bg-[#8A3E25] text-white',
        buttonHover: 'hover:bg-[#8A3E25]/15',
        iconColor: 'text-[#8A3E25]',
        divider: 'border-[#8A3E25]/30',
      },
    ]
    return designs[index % designs.length]
  }

  return (
    <NPISection className="bg-white py-16">
      <NPISectionHeader className="mb-12">
        <NPISectionTitle className="text-[#725242] text-4xl font-bold mb-6 font-npi">
          {title}
        </NPISectionTitle>
        <NPISectionDescription className="text-black text-lg font-medium max-w-4xl mx-auto leading-relaxed">
          {description}
        </NPISectionDescription>
      </NPISectionHeader>

      {/* Partners Grid - 3 cards per row for better size */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {partners.map((partner, index) => {
          const design = getCardDesign(index)
          return (
            <motion.div
              key={partner.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{
                y: -12,
                scale: 1.03,
                boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
                transition: { duration: 0.3, ease: 'easeOut' },
              }}
              className="group"
            >
              <NPICard
                className={`cursor-pointer h-full flex flex-col min-h-[480px] ${design.bg} ${design.border} border-3 transition-all duration-300 overflow-hidden group-hover:border-opacity-80 shadow-lg group-hover:shadow-2xl`}
                onClick={() => setSelectedPartner(partner)}
              >
                {/* Enhanced Header with Better Logo Presentation */}
                <NPICardHeader className="flex-shrink-0 p-6">
                  <div className="flex flex-col items-center text-center">
                    <div
                      className={`w-24 h-24 ${design.logoContainer} border-2 flex items-center justify-center mb-4 overflow-hidden transition-all duration-300 group-hover:scale-110 group-hover:shadow-md`}
                    >
                      <Image
                        src={partner.logo}
                        alt={`${partner.name} logo`}
                        width={96}
                        height={96}
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                    </div>
                    <NPICardTitle
                      className={`text-xl mb-3 leading-tight ${design.text} font-npi font-bold text-center min-h-[3rem] flex items-center justify-center`}
                    >
                      {partner.name}
                    </NPICardTitle>
                    <span
                      className={`px-3 py-1.5 text-sm font-semibold ${design.categoryBg} transition-all duration-300 group-hover:scale-105`}
                    >
                      {partner.category}
                    </span>
                  </div>
                </NPICardHeader>

                {/* Enhanced Content with Better Information Layout */}
                <NPICardContent className="p-6 pt-0 flex-1 flex flex-col">
                  <div className="space-y-4 flex-1">
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <MapPin className={`w-5 h-5 ${design.iconColor} flex-shrink-0`} />
                        <span className={`text-sm ${design.text} opacity-90 font-medium`}>
                          {partner.location}
                        </span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Calendar className={`w-5 h-5 ${design.iconColor} flex-shrink-0`} />
                        <span className={`text-sm ${design.text} opacity-90 font-medium`}>
                          Partner since {partner.partnershipSince}
                        </span>
                      </div>
                    </div>

                    <div className={`border-t ${design.divider} pt-4`}>
                      <p
                        className={`text-sm leading-relaxed ${design.text} opacity-95 font-medium`}
                      >
                        {partner.description}
                      </p>
                    </div>
                  </div>

                  {/* Enhanced Action Button */}
                  <div className="mt-6 pt-4 border-t border-current border-opacity-20">
                    <NPIButton
                      size="sm"
                      variant="outline"
                      className={`w-full text-sm font-semibold ${design.text} border-current ${design.buttonHover} transition-all duration-300 py-2.5 group-hover:scale-105 group-hover:shadow-md`}
                    >
                      View Partnership Details
                    </NPIButton>
                  </div>
                </NPICardContent>
              </NPICard>
            </motion.div>
          )
        })}
      </div>

      {/* Modal */}
      <AnimatePresence>
        {selectedPartner && (
          <motion.div
            className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSelectedPartner(null)}
          >
            <motion.div
              className="bg-white max-w-2xl w-full max-h-[90vh] overflow-y-auto border-2 border-[#725242]"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Modal Header */}
              <div className="bg-[#8A3E25] p-6 text-white">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 bg-white/20 border border-white/30 flex items-center justify-center overflow-hidden">
                      <Image
                        src={selectedPartner.logo}
                        alt={`${selectedPartner.name} logo`}
                        width={64}
                        height={64}
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold mb-1 font-npi">{selectedPartner.name}</h3>
                      <span className="px-3 py-1 bg-white/20 text-sm font-medium">
                        {selectedPartner.category}
                      </span>
                    </div>
                  </div>
                  <button
                    onClick={() => setSelectedPartner(null)}
                    className="p-2 hover:bg-white/20 transition-colors"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>
              </div>

              {/* Modal Content */}
              <div className="p-6 space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3 text-black font-npi">Partner Information</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-[#8A3E25]" />
                        <span className="text-black">{selectedPartner.location}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-[#8A3E25]" />
                        <span className="text-black">
                          Established {selectedPartner.established}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="w-4 h-4 text-[#8A3E25]" />
                        <span className="text-black">
                          Partner since {selectedPartner.partnershipSince}
                        </span>
                      </div>
                      {selectedPartner.website && (
                        <div className="flex items-center gap-2">
                          <ExternalLink className="w-4 h-4 text-[#25718A]" />
                          <a
                            href={selectedPartner.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-[#25718A] hover:underline transition-colors duration-300"
                          >
                            Visit Website
                          </a>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-3 text-black font-npi">Collaboration Areas</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedPartner.collaborationAreas.map((area, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-[#EFE3BA] text-[#725242] text-xs border border-[#725242]/20"
                        >
                          {area}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-3 text-black font-npi">About</h4>
                  <p className="text-black leading-relaxed">{selectedPartner.fullBio}</p>
                </div>

                <div>
                  <h4 className="font-semibold mb-3 text-black font-npi">Key Achievements</h4>
                  <ul className="space-y-2">
                    {selectedPartner.keyAchievements.map((achievement, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <div className="w-2 h-2 bg-[#8A3E25] mt-2 flex-shrink-0"></div>
                        <span className="text-black text-sm">{achievement}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </NPISection>
  )
}
