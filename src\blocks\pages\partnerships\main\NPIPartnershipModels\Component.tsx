'use client'

import React, { useState } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPICard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import { NPIModal } from '@/components/ui/npi-modal'
import { PartnershipApplicationModal } from '@/components/ui/npi-partnership-modal'
import Link from 'next/link'
import {
  Building,
  Users,
  Globe,
  Lightbulb,
  Heart,
  TrendingUp,
  ArrowRight,
  CheckCircle,
  Target,
  Eye,
} from 'lucide-react'

interface PartnershipModel {
  title: string
  description: string
  icon: React.ReactNode
  color: string
  benefits: string[]
  requirements: string[]
  examples: string[]
  commitmentLevel: 'Low' | 'Medium' | 'High'
  duration: string
  investmentRange: string
}

interface NPIPartnershipModelsProps {
  title?: string
  description?: string
  models?: PartnershipModel[]
}

export const NPIPartnershipModelsBlock: React.FC<NPIPartnershipModelsProps> = ({
  title = 'Partnership Models',
  description = 'Discover flexible partnership frameworks designed to maximize impact while respecting community rights and ensuring sustainable development.',
  models = [
    {
      title: 'Public-Private Partnership (PPP)',
      description:
        'Collaborative framework between government agencies, private sector, and NPI for large-scale natural products development.',
      icon: <Building className="w-8 h-8" />,
      color: 'from-primary to-primary/90',
      benefits: [
        'Access to government support and policy backing',
        'Shared risk and investment burden',
        'Regulatory compliance and legal protection',
        'Large-scale market access and distribution',
      ],
      requirements: [
        'Minimum investment of KES 100M',
        'Commitment to community benefit-sharing',
        'Environmental and social compliance',
        '5-year minimum partnership duration',
      ],
      examples: [
        'Aloe Vera Processing Facility (Baringo)',
        'Medicinal Plants Research Center (Nairobi)',
        'Essential Oils Export Hub (Mombasa)',
      ],
      commitmentLevel: 'High',
      duration: '5-10 years',
      investmentRange: 'KES 100M+',
    },
    {
      title: 'Community-Based Partnership',
      description:
        'Direct collaboration with local communities for grassroots natural products development and knowledge preservation.',
      icon: <Users className="w-8 h-8" />,
      color: 'from-secondary to-secondary/90',
      benefits: [
        'Direct community engagement and ownership',
        'Authentic traditional knowledge access',
        'Strong local support and sustainability',
        'Cultural preservation and empowerment',
      ],
      requirements: [
        'Community consent and participation',
        'Respect for traditional knowledge protocols',
        'Fair benefit-sharing agreements',
        'Cultural sensitivity training',
      ],
      examples: [
        'Ogiek Honey Cooperative (Nakuru)',
        'Maasai Traditional Medicine (Kajiado)',
        'Turkana Moringa Initiative',
      ],
      commitmentLevel: 'Medium',
      duration: '3-7 years',
      investmentRange: 'KES 1M-10M',
    },
    {
      title: 'Research & Academic Collaboration',
      description:
        'Partnership with universities and research institutions for scientific validation and innovation in natural products.',
      icon: <Lightbulb className="w-8 h-8" />,
      color: 'from-accent to-accent/90',
      benefits: [
        'Scientific validation and credibility',
        'Access to research facilities and expertise',
        'Publication and IP development opportunities',
        'Student and researcher engagement',
      ],
      requirements: [
        'Research ethics approval',
        'Data sharing agreements',
        'Publication and IP protocols',
        'Community consent for research',
      ],
      examples: [
        'University of Nairobi Medicinal Plants Study',
        'ICIPE Natural Products Research',
        'Kenyatta University Nutrition Studies',
      ],
      commitmentLevel: 'Medium',
      duration: '2-5 years',
      investmentRange: 'KES 5M-50M',
    },
    {
      title: 'International Development Partnership',
      description:
        'Collaboration with international organizations and donors for capacity building and knowledge exchange.',
      icon: <Globe className="w-8 h-8" />,
      color: 'from-npi-burgundy-500 to-npi-burgundy-600',
      benefits: [
        'Access to international funding',
        'Global knowledge and best practices',
        'Capacity building and training',
        'International market access',
      ],
      requirements: [
        'Alignment with development goals',
        'Transparency and accountability',
        'Local capacity building focus',
        'Sustainable development principles',
      ],
      examples: [
        'World Bank Natural Products Initiative',
        'GIZ Biodiversity Conservation Project',
        'UNIDO Industrial Development Program',
      ],
      commitmentLevel: 'High',
      duration: '3-8 years',
      investmentRange: 'KES 50M-500M',
    },
    {
      title: 'Social Impact Investment',
      description:
        'Investment model focused on generating positive social and environmental impact alongside financial returns.',
      icon: <Heart className="w-8 h-8" />,
      color: 'from-npi-gold-500 to-npi-gold-600',
      benefits: [
        'Measurable social and environmental impact',
        'Flexible financing terms',
        'Patient capital for long-term development',
        'Impact measurement and reporting',
      ],
      requirements: [
        'Clear impact metrics and targets',
        'Regular impact reporting',
        'Community benefit demonstration',
        'Environmental sustainability',
      ],
      examples: [
        "Women's Cooperative Microfinance",
        'Youth Enterprise Development Fund',
        'Community Forest Conservation Bonds',
      ],
      commitmentLevel: 'Medium',
      duration: '3-6 years',
      investmentRange: 'KES 10M-100M',
    },
    {
      title: 'Technology & Innovation Partnership',
      description:
        'Collaboration with technology companies for digital solutions and innovation in natural products sector.',
      icon: <TrendingUp className="w-8 h-8" />,
      color: 'from-npi-green-500 to-npi-green-600',
      benefits: [
        'Access to cutting-edge technology',
        'Digital transformation support',
        'Innovation and efficiency gains',
        'Market reach and scalability',
      ],
      requirements: [
        'Technology transfer agreements',
        'Local capacity building',
        'Data privacy and security',
        'Affordable technology access',
      ],
      examples: [
        'IKIA Digital Platform Development',
        'Blockchain Supply Chain Tracking',
        'AI-Powered Quality Control Systems',
      ],
      commitmentLevel: 'Medium',
      duration: '2-4 years',
      investmentRange: 'KES 20M-200M',
    },
  ],
}) => {
  const [isApplicationModalOpen, setIsApplicationModalOpen] = useState(false)
  const [selectedApplicationModel, setSelectedApplicationModel] = useState('')
  const [selectedDetailModel, setSelectedDetailModel] = useState<PartnershipModel | null>(null)

  const getCommitmentColor = (level: string) => {
    switch (level) {
      case 'Low':
        return 'text-green-700'
      case 'Medium':
        return 'text-yellow-700'
      case 'High':
        return 'text-red-700'
      default:
        return 'text-gray-700'
    }
  }

  const handlePartnershipApplication = (modelTitle?: string) => {
    setSelectedApplicationModel(modelTitle || '')
    setIsApplicationModalOpen(true)
  }

  const handleViewDetails = (model: PartnershipModel) => {
    setSelectedDetailModel(model)
  }

  const handleCloseDetailModal = () => {
    setSelectedDetailModel(null)
  }

  return (
    <NPISection className="">
      <NPISectionHeader>
        <NPISectionTitle className="text-[#8A3E25]">{title}</NPISectionTitle>
        <NPISectionDescription className="text-black">{description}</NPISectionDescription>
      </NPISectionHeader>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {models.map((model, index) => (
          <NPICard
            key={index}
            className="overflow-hidden hover:shadow-xl transition-all duration-300 h-full flex flex-col min-h-[600px] bg-white border-2 border-[#725242]/20 hover:border-[#8A3E25] hover:scale-105"
          >
            {/* Header with platform colors - no gradients */}
            <div className="bg-[#725242] p-4 text-white flex-shrink-0">
              <div className="flex flex-col items-center text-center mb-3">
                <div className="bg-white/20 p-2 mb-2">{model.icon}</div>
                <NPICardTitle className="text-white text-base mb-2 leading-tight font-bold">
                  {model.title}
                </NPICardTitle>
                <div className="flex flex-col gap-1">
                  <span
                    className={`px-2 py-1 text-xs font-medium ${getCommitmentColor(model.commitmentLevel)} bg-white/90`}
                  >
                    {model.commitmentLevel} Commitment
                  </span>
                  <span className="text-xs text-white/90 font-medium">{model.duration}</span>
                </div>
              </div>
              <p className="text-white/90 text-sm leading-tight font-medium text-center">
                {model.description}
              </p>
            </div>

            {/* Summary Content */}
            <NPICardContent className="p-4 flex-1 flex flex-col">
              <div className="space-y-3 flex-1">
                {/* Key Benefits Summary */}
                <div>
                  <h4 className="font-semibold mb-2 text-black font-medium text-sm flex items-center gap-1">
                    <CheckCircle className="w-4 h-4 text-[#25718A]" />
                    Key Benefits
                  </h4>
                  <ul className="space-y-1">
                    {model.benefits.slice(0, 3).map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="flex items-start gap-2 text-sm">
                        <div className="w-1.5 h-1.5 bg-[#8A3E25] mt-2 flex-shrink-0"></div>
                        <span className="text-[#725242] font-medium leading-tight">{benefit}</span>
                      </li>
                    ))}
                    {model.benefits.length > 3 && (
                      <li className="text-sm text-[#725242]/70 font-medium italic">
                        +{model.benefits.length - 3} more benefits
                      </li>
                    )}
                  </ul>
                </div>

                {/* Key Requirements Summary */}
                <div>
                  <h4 className="font-semibold mb-2 text-black font-medium text-sm flex items-center gap-1">
                    <Target className="w-4 h-4 text-[#8A3E25]" />
                    Key Requirements
                  </h4>
                  <ul className="space-y-1">
                    {model.requirements.slice(0, 2).map((requirement, reqIndex) => (
                      <li key={reqIndex} className="flex items-start gap-2 text-sm">
                        <div className="w-1.5 h-1.5 bg-[#725242] mt-2 flex-shrink-0"></div>
                        <span className="text-[#725242] font-medium leading-tight">
                          {requirement}
                        </span>
                      </li>
                    ))}
                    {model.requirements.length > 2 && (
                      <li className="text-sm text-[#725242]/70 font-medium italic">
                        +{model.requirements.length - 2} more requirements
                      </li>
                    )}
                  </ul>
                </div>

                {/* Investment Range */}
                <div className="bg-[#EFE3BA] p-3 border-2 border-[#725242]/20">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-black">Investment Range</span>
                    <span className="text-sm font-bold text-[#8A3E25]">
                      {model.investmentRange}
                    </span>
                  </div>
                </div>
              </div>

              <div className="mt-4 pt-3 border-t-2 border-[#725242]/20 space-y-2">
                <NPIButton
                  variant="outline"
                  size="sm"
                  className="w-full text-sm py-2 border-2 border-[#25718A] text-[#25718A] hover:bg-[#25718A] hover:text-white transition-colors"
                  onClick={() => handleViewDetails(model)}
                >
                  View Details <Eye className="w-4 h-4 ml-1" />
                </NPIButton>
                <NPIButton
                  size="sm"
                  className="w-full text-sm py-2 bg-[#8A3E25] hover:bg-[#8A3E25] text-white transition-colors"
                  onClick={() => handlePartnershipApplication(model.title)}
                >
                  Apply Now <ArrowRight className="w-4 h-4 ml-1" />
                </NPIButton>
              </div>
            </NPICardContent>
          </NPICard>
        ))}
      </div>

      {/* Call to Action */}
      <div className="mt-12 text-center">
        <NPICard className="bg-white border-2 border-[#8A3E25]/30">
          <NPICardContent className="p-8">
            <h3 className="text-2xl font-bold mb-4 text-[#8A3E25]">Ready to Partner with NPI?</h3>
            <p className="text-black mb-6 max-w-2xl mx-auto font-medium">
              Join us in transforming Kenya&apos;s natural products sector. Our flexible partnership
              models ensure that every collaboration creates lasting value for communities,
              partners, and the environment.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <NPIButton
                size="lg"
                className="bg-[#8A3E25] hover:bg-[#8A3E25] text-white px-8 py-3"
                onClick={() => handlePartnershipApplication()}
              >
                Start Partnership Application
              </NPIButton>
              <NPIButton
                asChild
                size="lg"
                className="border-2 border-[#25718A] text-[#25718A] hover:bg-[#25718A] hover:text-white px-8 py-3"
                variant="outline"
              >
                <Link href="/contact">Schedule Consultation</Link>
              </NPIButton>
            </div>
          </NPICardContent>
        </NPICard>
      </div>

      {/* Partnership Application Modal */}
      <PartnershipApplicationModal
        isOpen={isApplicationModalOpen}
        onClose={() => setIsApplicationModalOpen(false)}
        selectedModel={selectedApplicationModel}
      />

      {/* Partnership Detail Modal */}
      {selectedDetailModel && (
        <NPIModal
          isOpen={!!selectedDetailModel}
          onClose={handleCloseDetailModal}
          size="xl"
          className="bg-white"
        >
          <div className="p-6">
            {/* Header */}
            <div className="bg-[#725242] p-6 text-white mb-6 -m-6 mb-6">
              <div className="flex items-start gap-6">
                <div className="w-16 h-16 bg-white/20 flex items-center justify-center text-white">
                  {selectedDetailModel.icon}
                </div>
                <div className="flex-1">
                  <h2 className="text-3xl font-bold text-white mb-2">
                    {selectedDetailModel.title}
                  </h2>
                  <p className="text-white/90 text-lg leading-relaxed mb-3">
                    {selectedDetailModel.description}
                  </p>
                  <div className="flex gap-4">
                    <span
                      className={`px-3 py-1 text-sm font-medium ${getCommitmentColor(selectedDetailModel.commitmentLevel)} bg-white/90`}
                    >
                      {selectedDetailModel.commitmentLevel} Commitment
                    </span>
                    <span className="text-sm text-white/90 font-medium bg-white/20 px-3 py-1">
                      {selectedDetailModel.duration}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Content Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Benefits */}
              <div className="bg-[#EFE3BA] p-4 border-2 border-[#725242]/20">
                <h3 className="text-lg font-bold text-[#8A3E25] mb-3 flex items-center gap-2">
                  <CheckCircle className="w-5 h-5" />
                  Benefits
                </h3>
                <ul className="space-y-2">
                  {selectedDetailModel.benefits.map((benefit, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-[#8A3E25] mt-2 flex-shrink-0"></div>
                      <span className="text-black font-medium">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Requirements */}
              <div className="bg-white p-4 border-2 border-[#725242]/20">
                <h3 className="text-lg font-bold text-[#8A3E25] mb-3 flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  Requirements
                </h3>
                <ul className="space-y-2">
                  {selectedDetailModel.requirements.map((requirement, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-[#725242] mt-2 flex-shrink-0"></div>
                      <span className="text-black font-medium">{requirement}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Examples */}
              <div className="bg-white p-4 border-2 border-[#725242]/20">
                <h3 className="text-lg font-bold text-[#8A3E25] mb-3 flex items-center gap-2">
                  <Lightbulb className="w-5 h-5" />
                  Examples
                </h3>
                <ul className="space-y-2">
                  {selectedDetailModel.examples.map((example, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-[#25718A] mt-2 flex-shrink-0"></div>
                      <span className="text-black font-medium">{example}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Investment Range */}
              <div className="bg-[#EFE3BA] p-4 border-2 border-[#725242]/20">
                <h3 className="text-lg font-bold text-[#8A3E25] mb-3 flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  Investment Details
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-black font-medium">Investment Range:</span>
                    <span className="text-[#8A3E25] font-bold text-lg">
                      {selectedDetailModel.investmentRange}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-black font-medium">Duration:</span>
                    <span className="text-[#725242] font-bold">{selectedDetailModel.duration}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-black font-medium">Commitment Level:</span>
                    <span
                      className={`font-bold ${getCommitmentColor(selectedDetailModel.commitmentLevel)}`}
                    >
                      {selectedDetailModel.commitmentLevel}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="mt-6 flex gap-4 justify-center">
              <NPIButton
                size="lg"
                className="bg-[#8A3E25] hover:bg-[#725242] text-white px-8 py-3"
                onClick={() => {
                  handleCloseDetailModal()
                  handlePartnershipApplication(selectedDetailModel.title)
                }}
              >
                Apply for This Partnership
              </NPIButton>
              <NPIButton
                size="lg"
                variant="outline"
                className="border-2 border-[#25718A] text-[#25718A] hover:bg-[#25718A] hover:text-white px-8 py-3"
                onClick={handleCloseDetailModal}
              >
                Close
              </NPIButton>
            </div>
          </div>
        </NPIModal>
      )}
    </NPISection>
  )
}
